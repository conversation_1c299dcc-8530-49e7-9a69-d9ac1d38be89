const app = getApp();
// 引入通用常量
const { REGION_LIST, OCCUPATION_LIST } = require('../../../utils/constants');
// 引入产品类型和品牌配置
const productConfig = require('../../../config/productTypesBrands');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    title: '',
    scene: '',
    keyFactors: '',
    budget: {
      min: null,
      max: null,
      currency: 'CNY'
    },
    tags: [],
    // 标签状态数组
    tagStatus: {},
    // 标签选项 - 从配置文件读取
    tagOptions: Object.keys(productConfig.productTypes),

    // AI推荐相关
    showAiRecommendModal: false,
    questionInfo: {},
    loading: false,
    canRecommend: false,
    // 推荐结果
    recommendedProducts: [],
    // 产品对比相关
    compareVisible: false,
    isInCompareList: {} // 用于存储每个产品是否在对比列表中 {skuId: true/false}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 初始化标签状态
    const tagStatus = {};
    this.data.tagOptions.forEach(tag => {
      tagStatus[tag] = false;
    });
    
    this.setData({ 
      tagStatus
    });
    
    // 预先绑定函数上下文，确保onShow和onHide中引用的是同一个函数实例
    this.boundOnGlobalCompareUpdated = this.onGlobalCompareUpdated.bind(this);

    // 检查是否可以获取推荐
    this.checkCanRecommend();
  },

  onShow: function () {
    this.checkCompareStatus();
    // 监听全局对比状态更新事件
    app.getEventChannel().on('compareProductsUpdated', this.boundOnGlobalCompareUpdated);

    // 监听登录状态变化事件
    app.getEventChannel().on('loginStatusChanged', this.onLoginStatusChanged.bind(this));

    // 通知应用当前页面已显示，用于更新TabBar角标
    if (app && app.onPageShow) {
      app.onPageShow('pages/product/ai-recommend/ai-recommend');
    }
  },

  onHide: function () {
    // 移除事件监听
    app.getEventChannel().off('compareProductsUpdated', this.boundOnGlobalCompareUpdated);
    app.getEventChannel().off('loginStatusChanged', this.onLoginStatusChanged.bind(this));
  },

  /**
   * 输入标题
   */
  inputTitle: function(e) {
    this.setData({
      title: e.detail.value
    }, () => {
      this.checkCanRecommend();
    });
  },

  /**
   * 输入使用场景
   */
  inputScene: function(e) {
    this.setData({
      scene: e.detail.value
    });
  },

  /**
   * 输入关键考量因素
   */
  inputKeyFactors: function(e) {
    this.setData({
      keyFactors: e.detail.value
    });
  },
  
  /**
   * 输入预算最小值
   */
  inputBudgetMin: function(e) {
    this.setData({
      'budget.min': e.detail.value ? Number(e.detail.value) : null
    });
  },
  
  /**
   * 输入预算最大值
   */
  inputBudgetMax: function(e) {
    this.setData({
      'budget.max': e.detail.value ? Number(e.detail.value) : null
    });
  },
  
  /**
   * 选择标签
   */
  selectTag: function(e) {
    const { tag } = e.currentTarget.dataset;
    const { tags, tagStatus } = this.data;
    
    // 切换标签选中状态
    if (tagStatus[tag]) {
      // 取消选中
      const newTags = tags.filter(t => t !== tag);
      const newTagStatus = { ...tagStatus };
      newTagStatus[tag] = false;
      
      this.setData({
        tags: newTags,
        tagStatus: newTagStatus
      }, () => {
        this.checkCanRecommend();
        console.log('标签已取消选中:', tag, '当前选中标签:', this.data.tags);
      });
    } else {
      // 选中 - 对于AI推荐页面，只允许选择一个标签
      if (tags.length >= 1) {
        wx.showToast({
          title: '请只选择1个产品类别',
          icon: 'none'
        });
        return;
      }
      
      const newTags = [...tags, tag];
      const newTagStatus = { ...tagStatus };
      newTagStatus[tag] = true;
      
      this.setData({
        tags: newTags,
        tagStatus: newTagStatus
      }, () => {
        this.checkCanRecommend();
        console.log('标签已选중:', tag, '当前选중标签:', this.data.tags);
      });
    }
  },

  /**
   * 检查是否可以获取推荐
   */
  checkCanRecommend: function() {
    const { title, tags } = this.data;
    const canRecommend = title.trim().length > 0 && tags.length > 0;
    this.setData({ canRecommend });
  },

  /**
   * 获取AI推荐
   */
  getAiRecommendation: function() {
    const { title, scene, keyFactors, budget, tags } = this.data;
    
    // 验证必填项
    if (!title.trim()) {
      wx.showToast({
        title: '请输入需求描述',
        icon: 'none'
      });
      return;
    }
    
    if (tags.length === 0) {
      wx.showToast({
        title: '请选择产品类别',
        icon: 'none'
      });
      return;
    }
    
    // 构建问题信息
    const questionInfo = {
      title: title.trim(),
      scene: scene.trim(),
      keyFactors: keyFactors.trim(),
      budget,
      tags,
      selectedCategory: tags[0] // 传递选中的产品类别
    };
    
    this.setData({
      showAiRecommendModal: true,
      questionInfo,
      loading: false
    });
  },

  /**
   * 关闭AI推荐弹窗
   */
  closeAiRecommend: function() {
    this.setData({
      showAiRecommendModal: false,
      loading: false
    });
  },

  /**
   * 处理AI推荐结果
   */
  onAiRecommend: function(e) {
    const { success, data } = e.detail;
    
    if (!success || !data) {
      console.error('AI推荐事件数据格式错误:', e.detail);
      wx.showToast({
        title: '推荐数据格式错误',
        icon: 'none'
      });
      return;
    }
    
    console.log('AI推荐成功，原始数据:', data);
    
    try {
      // 从不同的数据结构中提取推荐产品列表
      let recommendedProducts = [];
      
      if (data.recommendedProducts && Array.isArray(data.recommendedProducts)) {
        recommendedProducts = data.recommendedProducts;
      } else if (data.products && Array.isArray(data.products)) {
        recommendedProducts = data.products;
      } else {
        console.error('推荐结果数据结构异常:', data);
        throw new Error('推荐结果数据格式错误');
      }
      
      if (recommendedProducts.length === 0) {
        console.warn('AI推荐返回的产品列表为空');
        throw new Error('没有找到合适的推荐产品，请调整筛选条件');
      }
      
      // 处理推荐产品数据
      const processedProducts = recommendedProducts.map((item, index) => {
        // 提取产品名称
        let productName = '';
        if (item.skuName && typeof item.skuName === 'string') {
          productName = item.skuName.trim();
        } else if (item.name && typeof item.name === 'string') {
          productName = item.name.trim();
        } else if (item.content && typeof item.content === 'string') {
          productName = item.content.trim();
        }
        
        if (!productName) {
          console.warn(`第${index + 1}个推荐产品名称为空:`, item);
          productName = `推荐产品${index + 1}`;
        }
        
        return {
          name: productName,
          hasRecommendReason: !!(item.recommendReason || item.highlights),
          recommendReason: item.recommendReason || '',
          highlights: item.highlights || [],
          expanded: false, // 默认不展开
          originalData: item // 保留原始数据用于其他操作
        };
      });
      
      console.log('处理后的推荐产品:', processedProducts);
      
      // 更新页面数据
      this.setData({
        recommendedProducts: processedProducts,
        showAiRecommendModal: false,
        loading: false
      }, () => {
        this.checkCompareStatus(); // 检查新推荐产品的对比状态
      });
      
      // 显示成功提示
      wx.showToast({
        title: `为您推荐了${processedProducts.length}款产品`,
        icon: 'success',
        duration: 2000
      });
      
    } catch (error) {
      console.error('处理AI推荐结果失败:', error);
      this.setData({
        loading: false,
        showAiRecommendModal: false
      });
      wx.showToast({
        title: error.message || '处理推荐结果失败，请重试',
        icon: 'none',
        duration: 3000
      });
    }
  },

  /**
   * 切换推荐理由展开状态
   */
  toggleReasonExpanded: function(e) {
    const { index } = e.currentTarget.dataset;
    const { recommendedProducts } = this.data;
    
    recommendedProducts[index].expanded = !recommendedProducts[index].expanded;
    this.setData({ recommendedProducts });
  },

  /**
   * 添加到对比或从对比中移除
   */
  addToCompare: function(e) {
    const { product } = e.currentTarget.dataset;
    const skuId = product.originalData.skuId; // 假设originalData中有skuId

    if (!skuId) {
      wx.showToast({
        title: '产品ID不存在，无法对比',
        icon: 'none'
      });
      return;
    }

    const isInCompare = app.isInCompare(skuId);

    if (isInCompare) {
      // 如果已在对比列表中，则移除
      const compareProducts = app.getCompareProducts();
      const index = compareProducts.findIndex(p => p.skuId === skuId);
      if (index > -1) {
        app.removeFromCompare(index);
        wx.showToast({ title: '已移出对比', icon: 'success' });
      }
    } else {
      // 如果不在对比列表中，则添加
      const productToAdd = {
        skuId: skuId,
        skuName: product.name,
        // 其他需要保存到对比列表的信息
      };
      const success = app.addToCompare(productToAdd);
      if (success) {
        wx.showToast({ title: '已加入对比', icon: 'success' });
      }
    }

    this.checkCompareStatus();
  },

  /**
   * 检查产品对比状态
   */
  checkCompareStatus: function() {
    const { recommendedProducts } = this.data;
    const isInCompareList = {};
    recommendedProducts.forEach(p => {
      const skuId = p.originalData.skuId;
      if (skuId) {
        isInCompareList[skuId] = app.isInCompare(skuId);
      }
    });
    this.setData({ isInCompareList });
  },

  /**
   * 切换对比面板的显示/隐藏
   */
  onToggleCompareVisible: function() {
    this.setData({ compareVisible: !this.data.compareVisible });
  },

  /**
   * 全局对比状态更新回调
   */
  onGlobalCompareUpdated: function() {
    this.checkCompareStatus();
  },

  /**
   * 监听登录状态变化事件
   */
  onLoginStatusChanged: function(isLoggedIn) {
    console.log('AI推荐页面监听到登录状态变化:', isLoggedIn);

    if (!isLoggedIn) {
      // 用户退出登录时，更新对比状态（对比列表已在app.js中清空）
      this.checkCompareStatus();
    }
  },

  /**
   * 查看产品详情
   */
  viewDetail: function(e) {
    const { product } = e.currentTarget.dataset;
    const skuName = product.name; // 后端需要skuName

    if (!skuName) {
      wx.showToast({
        title: '产品名称不存在，无法查看详情',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/product/product_detail/product_detail?productName=${skuName}`
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage: function () {
    return {
      title: 'AI智能推荐 - 选选',
      path: '/pages/product/ai-recommend/ai-recommend'
    };
  }
});