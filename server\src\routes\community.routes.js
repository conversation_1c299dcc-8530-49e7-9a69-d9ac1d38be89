const express = require('express');
const router = express.Router();
const { protect } = require('../middlewares/auth');
const questionController = require('../controllers/questionController');
const answerController = require('../controllers/answerController');
const commentController = require('../controllers/commentController');
const resultController = require('../controllers/resultController');

// 问题相关路由
router.route('/questions')
  .post(protect, questionController.createQuestion)
  .get(questionController.getQuestions);

// 添加搜索问题路由
router.route('/questions/search')
  .get(questionController.searchQuestions);

router.route('/questions/:id')
  // .get(protect, questionController.getQuestionById)
  .get(questionController.getQuestionById)
  .put(protect, questionController.updateQuestion)
  .delete(protect, questionController.deleteQuestion);

// 新增：关闭问题路由
router.route('/questions/:id/close')
  .put(protect, questionController.closeQuestion);

// 新增：关闭所有过期问题路由
router.route('/questions/close-expired')
  .post(protect, questionController.closeExpiredQuestions);

// 新增：问题产品对比路由
router.route('/questions/:id/compare')
  .post(protect, questionController.compareQuestionProducts);

// 新增：问题结果分析路由
router.route('/questions/:id/result')
  .get(protect, resultController.getQuestionResult);

// 新增：问题结果状态检查路由
router.route('/questions/:id/result/status')
  .get(protect, resultController.checkResultStatus);

// 新增：清除问题结果缓存路由
router.route('/questions/:id/result/cache')
  .delete(protect, resultController.clearResultCache);

// 新增：AI推荐产品选项路由（不需要用户认证）
router.route('/questions/ai-recommend')
  .post(questionController.aiRecommendProducts);

// 新增：检查AI推荐任务状态路由（不需要用户认证）
router.route('/questions/ai-recommend/status/:taskId')
  .get(questionController.checkAIRecommendStatus);

// 回答相关路由
router.route('/questions/:questionId/answers')
  .post(protect, answerController.createAnswer)
  // .get(protect, answerController.getAnswers);
  .get(answerController.getAnswers);

router.route('/answers/:id/like')
  .post(protect, answerController.likeAnswer);

// 新增：获取回答信息
router.route('/answers/:id')
  .get(protect, answerController.getAnswerInfo);

// 评论相关路由
router.route('/answers/:answerId/comments')
  .post(protect, commentController.createComment)
  .get(commentController.getComments);

router.route('/comments/:id')
  .delete(protect, commentController.deleteComment)
  .get(protect, commentController.getCommentInfo);

router.route('/comments/:commentId/replies')
  .get(commentController.getReplies);

module.exports = router;